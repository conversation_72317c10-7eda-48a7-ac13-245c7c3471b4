package main

import (
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/widget"
)

func main() {
	app := app.New()                       // 创建应用程序实例
	window := app.NewWindow("Hello world") // 创建窗口，标题为"Hello Wolrd"

	clock := widget.NewLabel("")
	updateTime(clock)
	go func() {
		for range time.Tick(time.Second) {
			updateTime(clock) // 每秒更新一次时间
		}
	}()

	window.SetContent(clock) // 往窗口中放入一个内容为"Hello world!"的标签控件
	window.Show()            //展示窗口1

	window2 := app.NewWindow("Larger Window")
	window2.SetContent(widget.NewButton("Open New", func() {
		window3 := app.NewWindow("Third")
		window3.SetContent(widget.NewLabel("Third"))
		window3.Show()
	}))
	window2.Resize(fyne.NewSize(100, 100)) // 设置窗口大小
	window2.Show()

	app.Run()

}

func updateTime(clock *widget.Label) {
	formatted := time.Now().Format("Time: 03:04:05") //获取格式化时间
	clock.SetText(formatted)
}
