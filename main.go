package main

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

func main() {
	myApp := app.New()
	myWindow := myApp.NewWindow("Fyne GUI 演示程序")
	myWindow.Resize(fyne.NewSize(800, 600))

	// 标题
	title := widget.NewLabelWithStyle("Fyne GUI 组件演示", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 按钮区域
	button1 := widget.NewButton("普通按钮", func() {
		dialog.ShowInformation("提示", "你点击了普通按钮！", myWindow)
	})

	button2 := widget.NewButtonWithIcon("图标按钮", theme.DocumentIcon(), func() {
		dialog.ShowInformation("提示", "你点击了图标按钮！", myWindow)
	})

	// 输入框
	entry := widget.NewEntry()
	entry.SetPlaceHolder("请输入文本...")

	multiEntry := widget.NewMultiLineEntry()
	multiEntry.SetPlaceHolder("多行文本输入框...")

	// 选择组件
	check := widget.NewCheck("复选框", func(checked bool) {
		if checked {
			fmt.Println("复选框被选中")
		} else {
			fmt.Println("复选框被取消")
		}
	})

	radio := widget.NewRadioGroup([]string{"选项1", "选项2", "选项3"}, func(selected string) {
		fmt.Printf("选择了: %s\n", selected)
	})

	// 下拉选择
	selectWidget := widget.NewSelect([]string{"苹果", "香蕉", "橙子", "葡萄"}, func(selected string) {
		fmt.Printf("选择了水果: %s\n", selected)
	})

	// 进度条
	progress := widget.NewProgressBar()
	progress.SetValue(0.6)

	// 滑块
	slider := widget.NewSlider(0, 100)
	slider.SetValue(50)
	slider.OnChanged = func(value float64) {
		progress.SetValue(value / 100)
	}

	// 列表
	list := widget.NewList(
		func() int { return 5 },
		func() fyne.CanvasObject {
			return widget.NewLabel("列表项")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(fmt.Sprintf("列表项 %d", id+1))
		},
	)

	// 表格
	table := widget.NewTable(
		func() (int, int) { return 3, 3 },
		func() fyne.CanvasObject {
			return widget.NewLabel("单元格")
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(fmt.Sprintf("行%d列%d", id.Row+1, id.Col+1))
		},
	)

	// 时间显示
	timeLabel := widget.NewLabel(time.Now().Format("2006-01-02 15:04:05"))
	go func() {
		for {
			time.Sleep(1 * time.Second)
			timeLabel.SetText(time.Now().Format("2006-01-02 15:04:05"))
		}
	}()

	// 创建标签页
	tabs := container.NewAppTabs(
		container.NewTabItem("基础组件", container.NewVBox(
			widget.NewCard("按钮", "", container.NewHBox(button1, button2)),
			widget.NewCard("输入框", "", container.NewVBox(entry, multiEntry)),
			widget.NewCard("选择组件", "", container.NewVBox(check, radio, selectWidget)),
		)),
		container.NewTabItem("进度与滑块", container.NewVBox(
			widget.NewCard("进度条", "", progress),
			widget.NewCard("滑块", "", slider),
			widget.NewCard("当前时间", "", timeLabel),
		)),
		container.NewTabItem("列表与表格", container.NewHSplit(
			widget.NewCard("列表", "", list),
			widget.NewCard("表格", "", table),
		)),
	)

	// 主布局
	content := container.NewVBox(
		title,
		tabs,
	)

	myWindow.SetContent(content)
	myWindow.ShowAndRun()
}
